import React, { useState, useCallback, memo } from "react";
import { FeishuAvatar } from "../../../components/FeishuAvatar";
import { Dialog } from "../../../components/Dialog";
import "./index.css";
import { Button } from "../../../components/Button";
import { DeleteIcon } from "../../../icons/DeleteIcon";

export interface MemberGroupCardProps {
  title: string | React.ReactNode;
  owner?: string;
  members: string[];
  deletable?: boolean;
  onDelete?: (memberId: string) => void;
}

interface MemberItemProps {
  memberId: string;
  deletable: boolean;
  onDeleteClick: (id: string) => void;
  isOwner?: boolean;
}

const MemberItem = memo<MemberItemProps>(({ memberId, deletable, onDeleteClick, isOwner = false }) => (
  <li className={`MemberCard ${isOwner ? 'MemberCard--owner' : ''}`}>
    {deletable && !isOwner && (
      <Button
        className="DeleteButton"
        icon={<DeleteIcon />}
        variant="icon"
        size="small"
        onClick={() => onDeleteClick(memberId)}
      />
    )}
    <FeishuAvatar uniqId={memberId} showId />
  </li>
));

MemberItem.displayName = 'MemberItem';

export const MemberGroupCard: React.FC<MemberGroupCardProps> = ({
  title,
  owner,
  members,
  deletable = false,
  onDelete,
}) => {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteId, setDeleteId] = useState('');

  const openDialog = useCallback((id: string) => {
    setShowDeleteDialog(true);
    setDeleteId(id);
  }, []);

  const handleConfirmDelete = useCallback(() => {
    onDelete?.(deleteId);
    setShowDeleteDialog(false);
    setDeleteId('');
  }, [onDelete, deleteId]);

  const handleCancelDelete = useCallback(() => {
    setShowDeleteDialog(false);
    setDeleteId('');
  }, []);

  const totalCount = members.length + (owner ? 1 : 0);

  return (
    <>
      <section className="MemberGroupCard">
        <header>
          <span className="GroupTitle">{title}</span>
          <span className="GroupCount">{totalCount}</span>
        </header>
        <ul>
          {owner && (
            <MemberItem
              memberId={owner}
              deletable={deletable}
              onDeleteClick={openDialog}
              isOwner={true}
            />
          )}
          {members.map((memberId) => (
            <MemberItem
              key={memberId}
              memberId={memberId}
              deletable={deletable}
              onDeleteClick={openDialog}
            />
          ))}
        </ul>
      </section>

      <Dialog
        open={showDeleteDialog}
        onClose={handleCancelDelete}
        title="确认删除"
        onOk={handleConfirmDelete}
        onCancel={handleCancelDelete}
        okText="删除"
        cancelText="取消"
        width={400}
      >
        <div className="DeleteConfirmContent">
          <span>确定要删除</span>
          <FeishuAvatar uniqId={deleteId} showId />
          <span>吗？</span>
        </div>
      </Dialog>
    </>
  );
};
