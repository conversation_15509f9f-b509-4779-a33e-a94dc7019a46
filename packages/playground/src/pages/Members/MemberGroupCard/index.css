
.MemberGroupCard {
  border-radius: 12px;
  min-width: 260px;
  display: flex;
  flex-direction: column;
  flex: 1;
}
.MemberGroupCard > header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 12px;
  font-weight: 600;
  color: #1e293b;
}
.GroupTitle {
  font-size: 1.1em;
}
.GroupCount {
  background: #e5e7eb;
  color: #374151;
  border-radius: 12px;
  font-size: 12px;
  padding: 2px 8px;
}
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.MemberCard {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 10px;
}
.DeleteButton {
  position: absolute;
  top: 0px;
  right: 0px;
  width: 5px;
  height: 5px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.2s ease;
  opacity: 0;
  transform: scale(0.8);
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.MemberCard:hover .DeleteButton {
  opacity: 1;
  transform: scale(1);
}

.DeleteButton:hover {
  background: #b91c1c;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.DeleteButton:active {
  transform: scale(0.95);
}